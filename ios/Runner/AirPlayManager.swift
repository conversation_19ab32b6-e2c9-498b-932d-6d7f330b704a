import Foundation
import AVFoundation
import MediaPlayer
import Flutter

@available(iOS 11.0, *)
class AirPlayManager: NSObject {
    private var player: AVPlayer?
    private var playerItem: AVPlayerItem?
    private var channel: FlutterMethodChannel
    private var routeDetector: AVRouteDetector?
    
    init(channel: FlutterMethodChannel) {
        self.channel = channel
        super.init()
        setupRouteDetector()
    }
    
    // MARK: - Route Detection
    
    private func setupRouteDetector() {
        routeDetector = AVRouteDetector()
        routeDetector?.isRouteDetectionEnabled = true
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(routeChanged),
            name: AVAudioSession.routeChangeNotification,
            object: nil
        )
        
        // Monitor external playback availability
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(externalPlaybackAvailabilityChanged),
            name: .MPVolumeViewWirelessRoutesAvailableDidChange,
            object: nil
        )
    }
    
    @objc private func routeChanged(notification: Notification) {
        DispatchQueue.main.async {
            self.notifyAirPlayStateChanged()
        }
    }
    
    @objc private func externalPlaybackAvailabilityChanged() {
        DispatchQueue.main.async {
            self.notifyAirPlayAvailabilityChanged()
        }
    }
    
    private func notifyAirPlayAvailabilityChanged() {
        let isAvailable = isAirPlayAvailable()
        channel.invokeMethod("onAirPlayAvailabilityChanged", arguments: isAvailable)
    }
    
    private func notifyAirPlayStateChanged() {
        let isActive = isAirPlayActive()
        let deviceName = getConnectedAirPlayDeviceName()
        
        let data: [String: Any] = [
            "isActive": isActive,
            "deviceName": deviceName ?? NSNull()
        ]
        
        channel.invokeMethod("onAirPlayStateChanged", arguments: data)
    }
    
    // MARK: - AirPlay Status Methods
    
    func isAirPlayAvailable() -> Bool {
        // Check if wireless routes are available
        let volumeView = MPVolumeView()
        return volumeView.areWirelessRoutesAvailable
    }
    
    func isAirPlayActive() -> Bool {
        guard let player = player else { return false }
        return player.isExternalPlaybackActive
    }
    
    private func getConnectedAirPlayDeviceName() -> String? {
        let audioSession = AVAudioSession.sharedInstance()
        let currentRoute = audioSession.currentRoute
        
        for output in currentRoute.outputs {
            if output.portType == .airPlay {
                return output.portName
            }
        }
        return nil
    }
    
    // MARK: - AirPlay Control Methods
    
    func showAirPlaySelector() {
        DispatchQueue.main.async {
            let routePickerView = AVRoutePickerView()
            routePickerView.activeTintColor = UIColor.systemBlue
            routePickerView.backgroundColor = UIColor.clear
            
            // Find the current view controller
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first,
               let rootViewController = window.rootViewController {
                
                // Create a temporary container for the route picker
                let containerView = UIView(frame: CGRect(x: 0, y: 0, width: 44, height: 44))
                routePickerView.frame = containerView.bounds
                containerView.addSubview(routePickerView)
                
                // Add to view hierarchy temporarily
                rootViewController.view.addSubview(containerView)
                
                // Trigger the route picker
                for subview in routePickerView.subviews {
                    if let button = subview as? UIButton {
                        button.sendActions(for: .touchUpInside)
                        break
                    }
                }
                
                // Remove the temporary container after a delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    containerView.removeFromSuperview()
                }
            }
        }
    }
    
    func startAirPlay(url: String, headers: [String: String]?) -> Bool {
        guard let videoURL = URL(string: url) else {
            print("Invalid URL: \(url)")
            return false
        }
        
        // Create player item with headers if provided
        let asset: AVURLAsset
        if let headers = headers, !headers.isEmpty {
            let options = ["AVURLAssetHTTPHeaderFieldsKey": headers]
            asset = AVURLAsset(url: videoURL, options: options)
        } else {
            asset = AVURLAsset(url: videoURL)
        }
        
        playerItem = AVPlayerItem(asset: asset)
        player = AVPlayer(playerItem: playerItem)
        
        // Enable external playback (AirPlay)
        player?.allowsExternalPlayback = true
        player?.usesExternalPlaybackWhileExternalScreenIsActive = true
        
        // Start playback
        player?.play()
        
        return true
    }
    
    func stopAirPlay() {
        player?.pause()
        player = nil
        playerItem = nil
    }
    
    func syncPosition(positionMs: Int) {
        guard let player = player else { return }
        
        let time = CMTime(value: Int64(positionMs), timescale: 1000)
        player.seek(to: time)
    }
    
    func setPlaybackRate(rate: Double) {
        player?.rate = Float(rate)
    }
    
    func getPlaybackPosition() -> Int {
        guard let player = player else { return 0 }
        
        let time = player.currentTime()
        return Int(CMTimeGetSeconds(time) * 1000)
    }
    
    // MARK: - Cleanup
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        routeDetector?.isRouteDetectionEnabled = false
        player = nil
        playerItem = nil
    }
}

// MARK: - Flutter Method Channel Handler

@available(iOS 11.0, *)
extension AirPlayManager {
    func handleMethodCall(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "isAirPlayAvailable":
            result(isAirPlayAvailable())
            
        case "isAirPlayActive":
            result(isAirPlayActive())
            
        case "showAirPlaySelector":
            showAirPlaySelector()
            result(nil)
            
        case "startAirPlay":
            guard let args = call.arguments as? [String: Any],
                  let url = args["url"] as? String else {
                result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing URL", details: nil))
                return
            }
            
            let headers = args["headers"] as? [String: String]
            let success = startAirPlay(url: url, headers: headers)
            result(success)
            
        case "stopAirPlay":
            stopAirPlay()
            result(nil)
            
        case "syncPosition":
            guard let args = call.arguments as? [String: Any],
                  let position = args["position"] as? Int else {
                result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing position", details: nil))
                return
            }
            
            syncPosition(positionMs: position)
            result(nil)
            
        case "setPlaybackRate":
            guard let args = call.arguments as? [String: Any],
                  let rate = args["rate"] as? Double else {
                result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing rate", details: nil))
                return
            }
            
            setPlaybackRate(rate: rate)
            result(nil)
            
        case "getPlaybackPosition":
            result(getPlaybackPosition())
            
        default:
            result(FlutterMethodNotImplemented)
        }
    }
}
