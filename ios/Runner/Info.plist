<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Mangayomi</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>mangayomi</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UISupportsDocumentBrowser</key>
	<true/>
	<key>FlutterDeepLinkingEnabled</key>
	<false/>
	<!-- AirPlay permissions -->
	<key>NSLocalNetworkUsageDescription</key>
	<string>This app uses the local network to discover and connect to AirPlay devices for video streaming.</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_airplay._tcp</string>
		<string>_raop._tcp</string>
	</array>
    <key>NSUbiquitousContainers</key>
    <dict>
        <key>iCloud.com.dvillegas.mangayomi</key>
        <dict>
            <key>NSUbiquitousContainerIsDocumentScopePublic</key>
            <true/>
            <key>NSUbiquitousContainerName</key>
            <string>Aniverse</string>
            <key>NSUbiquitousContainerSupportedFolderLevels</key>
            <string>Any</string>
        </dict>
    </dict>
	<key>GIDClientID</key>
	<string>237761209275-312lgtievc5q8m3b92o7ogu2llu7pcib.apps.googleusercontent.com</string>
	<key>CFBundleURLTypes</key>
    <array>
        <dict>
            <key>CFBundleURLName</key>
            <string>mangayomi</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>mangayomi</string>
            </array>
        </dict>
        <dict>
            <key>CFBundleURLName</key>
            <string>google</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>com.googleusercontent.apps.237761209275-312lgtievc5q8m3b92o7ogu2llu7pcib</string>
            </array>
        </dict>
    </array>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeIdentifier</key>
			<string>org.comicbook.cbz</string>
			<key>UTTypeDescription</key>
			<string>Comic Book Archive</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>cbz</string>
				</array>
				<key>public.mime-type</key>
				<string>application/x-cbz</string>
			</dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
				<string>public.zip-archive</string>
			</array>
		</dict>
	</array>

	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>Comic Book Archive</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.comicbook.cbz</string>
			</array>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
		</dict>
	</array>
</dict>
</plist>
