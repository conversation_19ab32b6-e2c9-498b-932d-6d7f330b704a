import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:mangayomi/services/airplay_service.dart';

void main() {
  group('AirPlay Service Tests', () {

    setUp(() {
      // Reset method channel handlers before each test
      const MethodChannel channel = MethodChannel('com.dvillegas.mangayomi/airplay');
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, null);
    });

    testWidgets('AirPlay service initializes correctly', (WidgetTester tester) async {
      // Mock the method channel
      const MethodChannel channel = MethodChannel('com.dvillegas.mangayomi/airplay');
      
      // Set up mock responses
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'isAirPlayAvailable':
              return true;
            case 'isAirPlayActive':
              return false;
            default:
              return null;
          }
        },
      );

      // Initialize the service
      await airPlayService.initialize();

      // Verify initial state
      expect(airPlayService.isAvailable, isTrue);
      expect(airPlayService.isActive, isFalse);
      expect(airPlayService.connectedDeviceName, isNull);
    });

    testWidgets('AirPlay device selector can be shown', (WidgetTester tester) async {
      const MethodChannel channel = MethodChannel('com.dvillegas.mangayomi/airplay');
      bool selectorShown = false;
      
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == 'showAirPlaySelector') {
            selectorShown = true;
            return null;
          }
          return null;
        },
      );

      await airPlayService.showDeviceSelector();
      expect(selectorShown, isTrue);
    });

    testWidgets('AirPlay streaming can be started', (WidgetTester tester) async {
      const MethodChannel channel = MethodChannel('com.dvillegas.mangayomi/airplay');
      String? streamedUrl;
      Map<String, String>? streamedHeaders;
      
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == 'startAirPlay') {
            final args = methodCall.arguments as Map<String, dynamic>;
            streamedUrl = args['url'] as String;
            streamedHeaders = Map<String, String>.from(args['headers'] as Map);
            return true;
          }
          return null;
        },
      );

      const testUrl = 'https://example.com/video.mp4';
      const testHeaders = {'Authorization': 'Bearer token123'};
      
      final success = await airPlayService.startStreaming(testUrl, headers: testHeaders);
      
      expect(success, isTrue);
      expect(streamedUrl, equals(testUrl));
      expect(streamedHeaders, equals(testHeaders));
    });

    testWidgets('AirPlay position can be synced', (WidgetTester tester) async {
      const MethodChannel channel = MethodChannel('com.dvillegas.mangayomi/airplay');
      int? syncedPosition;
      
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == 'syncPosition') {
            final args = methodCall.arguments as Map<String, dynamic>;
            syncedPosition = args['position'] as int;
            return null;
          }
          return null;
        },
      );

      const testPosition = Duration(minutes: 5, seconds: 30);
      await airPlayService.syncPosition(testPosition);
      
      expect(syncedPosition, equals(testPosition.inMilliseconds));
    });

    testWidgets('AirPlay streaming can be stopped', (WidgetTester tester) async {
      const MethodChannel channel = MethodChannel('com.dvillegas.mangayomi/airplay');
      bool streamingStopped = false;
      
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == 'stopAirPlay') {
            streamingStopped = true;
            return null;
          }
          return null;
        },
      );

      await airPlayService.stopStreaming();
      expect(streamingStopped, isTrue);
    });

    testWidgets('AirPlay playback rate can be set', (WidgetTester tester) async {
      const MethodChannel channel = MethodChannel('com.dvillegas.mangayomi/airplay');
      double? setRate;
      
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == 'setPlaybackRate') {
            final args = methodCall.arguments as Map<String, dynamic>;
            setRate = args['rate'] as double;
            return null;
          }
          return null;
        },
      );

      const testRate = 1.5;
      await airPlayService.setPlaybackRate(testRate);
      
      expect(setRate, equals(testRate));
    });

    testWidgets('AirPlay playback position can be retrieved', (WidgetTester tester) async {
      const MethodChannel channel = MethodChannel('com.dvillegas.mangayomi/airplay');
      
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == 'getPlaybackPosition') {
            return 150000; // 2.5 minutes in milliseconds
          }
          return null;
        },
      );

      final position = await airPlayService.getPlaybackPosition();
      expect(position, equals(const Duration(milliseconds: 150000)));
    });
  });
}
